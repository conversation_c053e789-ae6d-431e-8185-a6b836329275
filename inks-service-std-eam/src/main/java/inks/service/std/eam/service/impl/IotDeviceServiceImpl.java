package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.IotDeviceEntity;
import inks.service.std.eam.domain.pojo.IotDevicePojo;
import inks.service.std.eam.domain.vo.DeviceTokenVO;
import inks.service.std.eam.mapper.IotDeviceMapper;
import inks.service.std.eam.mqtt.auth.TokenBasedMqttAuthHandler;
import inks.service.std.eam.mqtt.service.MqttConnectionManager;
import inks.service.std.eam.service.IotDeviceService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 设备基本信息表(IotDevice)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:13
 */
@Service("iotDeviceService")
public class IotDeviceServiceImpl implements IotDeviceService {
    @Resource
    private IotDeviceMapper iotDeviceMapper;

    @Resource
    private TokenBasedMqttAuthHandler tokenBasedMqttAuthHandler;

    @Resource
    private MqttConnectionManager mqttConnectionManager;

    private final static Logger logger = LoggerFactory.getLogger(IotDeviceServiceImpl.class);

    @Override
    public IotDevicePojo getEntity(String key, String tid) {
        return this.iotDeviceMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotDevicePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotDevicePojo> lst = iotDeviceMapper.getPageList(queryParam);
            PageInfo<IotDevicePojo> pageInfo = new PageInfo<IotDevicePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotDevicePojo insert(IotDevicePojo iotDevicePojo) {
        //初始化NULL字段
        cleanNull(iotDevicePojo);
        IotDeviceEntity iotDeviceEntity = new IotDeviceEntity();
        BeanUtils.copyProperties(iotDevicePojo,iotDeviceEntity);
          //生成雪花id
          iotDeviceEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotDeviceEntity.setRevision(1);  //乐观锁
          this.iotDeviceMapper.insert(iotDeviceEntity);

        // 同步token到Redis
        syncTokenToRedis(iotDeviceEntity.getId(), iotDevicePojo.getToken(), null);

        return this.getEntity(iotDeviceEntity.getId(),iotDeviceEntity.getTenantid());
    }


    @Override
    public IotDevicePojo update(IotDevicePojo iotDevicePojo) {
        // 获取更新前的设备信息，用于比较token是否变更
        IotDevicePojo oldDevice = this.getEntity(iotDevicePojo.getId(), iotDevicePojo.getTenantid());
        String oldToken = oldDevice != null ? oldDevice.getToken() : null;

        IotDeviceEntity iotDeviceEntity = new IotDeviceEntity();
        BeanUtils.copyProperties(iotDevicePojo,iotDeviceEntity);
        this.iotDeviceMapper.update(iotDeviceEntity);

        // 同步token变更到Redis
        syncTokenToRedis(iotDevicePojo.getId(), iotDevicePojo.getToken(), oldToken);

        return this.getEntity(iotDeviceEntity.getId(),iotDeviceEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        // 删除前获取设备信息，用于清理Redis中的token
        IotDevicePojo device = this.getEntity(key, tid);
        String oldToken = device != null ? device.getToken() : null;

        int result = this.iotDeviceMapper.delete(key,tid);

        // 从Redis中删除token
        if (result > 0) {
            syncTokenToRedis(key, null, oldToken);
        }

        return result;
    }
    

    private static void cleanNull(IotDevicePojo iotDevicePojo) {
        if(iotDevicePojo.getAdditionalinfo()==null) iotDevicePojo.setAdditionalinfo("");
        if(iotDevicePojo.getCustomerid()==null) iotDevicePojo.setCustomerid("");
        if(iotDevicePojo.getDeviceprofileid()==null) iotDevicePojo.setDeviceprofileid("");
        if(StringUtils.isBlank(iotDevicePojo.getDevicedata())) iotDevicePojo.setDevicedata("{}");
        if(iotDevicePojo.getDevtype()==null) iotDevicePojo.setDevtype("");
        if(iotDevicePojo.getDevsn()==null) iotDevicePojo.setDevsn("");
        if(iotDevicePojo.getDevname()==null) iotDevicePojo.setDevname("");
        if(iotDevicePojo.getDevlabel()==null) iotDevicePojo.setDevlabel("");
        if(iotDevicePojo.getToken()==null) iotDevicePojo.setToken("");
        if(iotDevicePojo.getFirmwareid()==null) iotDevicePojo.setFirmwareid("");
        if(iotDevicePojo.getSoftwareid()==null) iotDevicePojo.setSoftwareid("");
        if(iotDevicePojo.getExternalid()==null) iotDevicePojo.setExternalid("");
        if(iotDevicePojo.getRemark()==null) iotDevicePojo.setRemark("");
        if(iotDevicePojo.getRownum()==null) iotDevicePojo.setRownum(0);
        if(iotDevicePojo.getCreateby()==null) iotDevicePojo.setCreateby("");
        if(iotDevicePojo.getCreatebyid()==null) iotDevicePojo.setCreatebyid("");
        if(iotDevicePojo.getCreatedate()==null) iotDevicePojo.setCreatedate(new Date());
        if(iotDevicePojo.getLister()==null) iotDevicePojo.setLister("");
        if(iotDevicePojo.getListerid()==null) iotDevicePojo.setListerid("");
        if(iotDevicePojo.getModifydate()==null) iotDevicePojo.setModifydate(new Date());
        if(iotDevicePojo.getCustom1()==null) iotDevicePojo.setCustom1("");
        if(iotDevicePojo.getCustom2()==null) iotDevicePojo.setCustom2("");
        if(iotDevicePojo.getCustom3()==null) iotDevicePojo.setCustom3("");
        if(iotDevicePojo.getCustom4()==null) iotDevicePojo.setCustom4("");
        if(iotDevicePojo.getCustom5()==null) iotDevicePojo.setCustom5("");
        if(iotDevicePojo.getCustom6()==null) iotDevicePojo.setCustom6("");
        if(iotDevicePojo.getCustom7()==null) iotDevicePojo.setCustom7("");
        if(iotDevicePojo.getCustom8()==null) iotDevicePojo.setCustom8("");
        if(iotDevicePojo.getCustom9()==null) iotDevicePojo.setCustom9("");
        if(iotDevicePojo.getCustom10()==null) iotDevicePojo.setCustom10("");
        if(iotDevicePojo.getTenantid()==null) iotDevicePojo.setTenantid("");
        if(iotDevicePojo.getTenantname()==null) iotDevicePojo.setTenantname("");
        if(iotDevicePojo.getRevision()==null) iotDevicePojo.setRevision(0);
   }

    @Override
    public IotDevicePojo getEntitybySn(String sn) {
        return this.iotDeviceMapper.getEntitybySn(sn);
    }

    @Override
    public IotDevicePojo getEntityByToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }
        return this.iotDeviceMapper.getEntityByToken(token.trim());
    }

    @Override
    public List<DeviceTokenVO> getAllDevicesWithToken() {
        return this.iotDeviceMapper.getAllDevicesWithToken();
    }

    /**
     * 同步token变更到Redis并处理MQTT连接
     * @param deviceId 设备ID
     * @param newToken 新token（null表示删除）
     * @param oldToken 旧token（用于清理）
     */
    private void syncTokenToRedis(String deviceId, String newToken, String oldToken) {
        try {
            // 1. 处理旧token和连接
            if (oldToken != null && !oldToken.trim().isEmpty()) {
                // 主动断开使用旧token的MQTT连接
                boolean disconnected = mqttConnectionManager.disconnectByToken(oldToken,
                    "设备token已变更，旧连接已失效");
                if (disconnected) {
                    logger.info("断开旧token连接成功 - deviceId:{}, oldToken:{}", deviceId, oldToken);
                } else {
                    logger.warn("断开旧token连接失败或连接不存在 - deviceId:{}, oldToken:{}", deviceId, oldToken);
                }

                // 从Redis清理旧token
                tokenBasedMqttAuthHandler.removeTokenFromRedis(oldToken);
                logger.info("清理旧token - deviceId:{}, oldToken:{}", deviceId, oldToken);
            }

            // 2. 处理设备删除的情况
            if (newToken == null) {
                // 设备被删除，断开所有相关连接
                boolean disconnected = mqttConnectionManager.disconnectByDeviceId(deviceId,
                    "设备已被删除");
                if (disconnected) {
                    logger.info("断开设备连接成功 - deviceId:{}", deviceId);
                } else {
                    logger.warn("断开设备连接失败或连接不存在 - deviceId:{}", deviceId);
                }
            }

            // 3. 添加新token
            if (newToken != null && !newToken.trim().isEmpty()) {
                tokenBasedMqttAuthHandler.addTokenToRedis(newToken, deviceId);
                logger.info("添加新token - deviceId:{}, newToken:{}", deviceId, newToken);
            }

        } catch (Exception e) {
            logger.error("同步token到Redis失败 - deviceId:{}, newToken:{}, oldToken:{}, error:{}",
                        deviceId, newToken, oldToken, e.getMessage(), e);
        }
    }
}

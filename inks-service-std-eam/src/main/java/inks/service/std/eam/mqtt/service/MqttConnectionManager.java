package inks.service.std.eam.mqtt.service;

import org.dromara.mica.mqtt.spring.server.MqttServerTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.tio.core.ChannelContext;
import org.tio.core.Tio;

import javax.annotation.Resource;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MQTT连接管理服务
 * 用于管理MQTT客户端连接，支持主动断开连接
 * 
 * <AUTHOR>
 */
@Service
public class MqttConnectionManager {
    
    private static final Logger logger = LoggerFactory.getLogger(MqttConnectionManager.class);
    
    @Resource
    private MqttServerTemplate mqttServerTemplate;
    
    /** 存储token到ChannelContext的映射，用于快速查找连接 */
    private final ConcurrentHashMap<String, ChannelContext> tokenToContextMap = new ConcurrentHashMap<>();
    
    /** 存储deviceId到token的映射，用于设备删除时查找 */
    private final ConcurrentHashMap<String, String> deviceToTokenMap = new ConcurrentHashMap<>();
    
    /**
     * 注册MQTT连接
     * 在客户端认证成功时调用
     * @param token 设备token
     * @param deviceId 设备ID
     * @param context 连接上下文
     */
    public void registerConnection(String token, String deviceId, ChannelContext context) {
        if (token != null && !token.trim().isEmpty() && context != null) {
            tokenToContextMap.put(token, context);
            if (deviceId != null && !deviceId.trim().isEmpty()) {
                deviceToTokenMap.put(deviceId, token);
            }
            logger.info("注册MQTT连接 - token:{}, deviceId:{}, clientId:{}", 
                       token, deviceId, context.get("clientId"));
        }
    }
    
    /**
     * 注销MQTT连接
     * 在客户端断开时调用
     * @param token 设备token
     * @param deviceId 设备ID
     */
    public void unregisterConnection(String token, String deviceId) {
        if (token != null && !token.trim().isEmpty()) {
            tokenToContextMap.remove(token);
            logger.info("注销MQTT连接 - token:{}, deviceId:{}", token, deviceId);
        }
        if (deviceId != null && !deviceId.trim().isEmpty()) {
            deviceToTokenMap.remove(deviceId);
        }
    }
    
    /**
     * 通过token主动断开MQTT连接
     * @param token 设备token
     * @param reason 断开原因
     * @return 是否成功断开
     */
    public boolean disconnectByToken(String token, String reason) {
        if (token == null || token.trim().isEmpty()) {
            logger.warn("断开连接失败：token为空");
            return false;
        }
        
        ChannelContext context = tokenToContextMap.get(token);
        if (context != null) {
            try {
                logger.info("主动断开MQTT连接 - token:{}, reason:{}, clientId:{}", 
                           token, reason, context.get("clientId"));
                
                // 从映射中移除
                tokenToContextMap.remove(token);
                String deviceId = (String) context.get("deviceId");
                if (deviceId != null) {
                    deviceToTokenMap.remove(deviceId);
                }
                
                // 关闭连接
                Tio.close(context, reason);
                return true;
                
            } catch (Exception e) {
                logger.error("断开MQTT连接异常 - token:{}, error:{}", token, e.getMessage(), e);
                return false;
            }
        } else {
            logger.warn("未找到token对应的连接 - token:{}", token);
            return false;
        }
    }
    
    /**
     * 通过设备ID主动断开MQTT连接
     * @param deviceId 设备ID
     * @param reason 断开原因
     * @return 是否成功断开
     */
    public boolean disconnectByDeviceId(String deviceId, String reason) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            logger.warn("断开连接失败：设备ID为空");
            return false;
        }
        
        String token = deviceToTokenMap.get(deviceId);
        if (token != null) {
            return disconnectByToken(token, reason);
        } else {
            logger.warn("未找到设备ID对应的token - deviceId:{}", deviceId);
            return false;
        }
    }
    
    /**
     * 获取当前连接数量
     * @return 连接数量
     */
    public int getConnectionCount() {
        return tokenToContextMap.size();
    }
    
    /**
     * 获取所有连接的token
     * @return token集合
     */
    public Set<String> getAllConnectedTokens() {
        return tokenToContextMap.keySet();
    }
    
    /**
     * 检查token是否在线
     * @param token 设备token
     * @return 是否在线
     */
    public boolean isTokenOnline(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }
        
        ChannelContext context = tokenToContextMap.get(token);
        return context != null && !context.isClosed();
    }
    
    /**
     * 检查设备是否在线
     * @param deviceId 设备ID
     * @return 是否在线
     */
    public boolean isDeviceOnline(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            return false;
        }
        
        String token = deviceToTokenMap.get(deviceId);
        return token != null && isTokenOnline(token);
    }
    
    /**
     * 清理无效连接
     * 定期清理已断开但未正确注销的连接
     */
    public void cleanupInvalidConnections() {
        logger.info("开始清理无效MQTT连接...");
        
        int cleanedCount = 0;
        for (String token : tokenToContextMap.keySet()) {
            ChannelContext context = tokenToContextMap.get(token);
            if (context == null || context.isClosed()) {
                tokenToContextMap.remove(token);
                // 同时清理设备映射
                String deviceId = context != null ? (String) context.get("deviceId") : null;
                if (deviceId != null) {
                    deviceToTokenMap.remove(deviceId);
                }
                cleanedCount++;
                logger.debug("清理无效连接 - token:{}, deviceId:{}", token, deviceId);
            }
        }
        
        logger.info("清理无效MQTT连接完成，清理数量：{}", cleanedCount);
    }
}

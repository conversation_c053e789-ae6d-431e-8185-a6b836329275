package inks.service.std.eam.mqtt.controller;

import inks.service.std.eam.mqtt.service.MqttClientService;
import inks.service.std.eam.mqtt.service.MqttStatsService;
import org.dromara.mica.mqtt.spring.server.MqttServerTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MQTT HTTP API控制器 - 提供简化的API接口（无认证）
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1")
public class MqttApiController {

    private static final Logger logger = LoggerFactory.getLogger(MqttApiController.class);

    @Autowired
    private MqttServerTemplate mqttServerTemplate;

    @Autowired
    private MqttStatsService mqttStatsService;

    @Autowired
    private MqttClientService mqttClientService;

    // 请求频率限制 - 存储客户端IP和最后请求时间
    private final Map<String, Long> requestTimestamps = new ConcurrentHashMap<>();
    private static final long MIN_REQUEST_INTERVAL = 2000; // 最小请求间隔2秒

    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "ok");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    /**
     * 获取所有API端点列表
     */
    @GetMapping("/endpoints")
    public Map<String, Object> getEndpoints() {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, String>> endpoints = new ArrayList<>();
        
        // 添加所有支持的端点
        endpoints.add(createEndpoint("GET", "/api/v1/endpoints"));
        endpoints.add(createEndpoint("GET", "/api/v1/clients"));
        endpoints.add(createEndpoint("GET", "/api/v1/clients/info"));
        endpoints.add(createEndpoint("GET", "/api/v1/client/subscriptions"));
        endpoints.add(createEndpoint("GET", "/api/v1/stats"));
        endpoints.add(createEndpoint("POST", "/api/v1/mqtt/publish"));
        endpoints.add(createEndpoint("POST", "/api/v1/mqtt/publish/batch"));
        endpoints.add(createEndpoint("POST", "/api/v1/mqtt/subscribe"));
        endpoints.add(createEndpoint("POST", "/api/v1/mqtt/subscribe/batch"));
        endpoints.add(createEndpoint("POST", "/api/v1/mqtt/unsubscribe"));
        endpoints.add(createEndpoint("POST", "/api/v1/mqtt/unsubscribe/batch"));
        endpoints.add(createEndpoint("POST", "/api/v1/clients/delete"));
        endpoints.add(createEndpoint("GET", "/api/v1/clients/subscriptions"));
        
        result.put("code", 1);
        result.put("data", endpoints);
        return result;
    }

    /**
     * 检查请求频率限制
     */
    private boolean isRequestTooFrequent(String clientKey) {
        long currentTime = System.currentTimeMillis();
        Long lastRequestTime = requestTimestamps.get(clientKey);

        if (lastRequestTime != null && (currentTime - lastRequestTime) < MIN_REQUEST_INTERVAL) {
            return true;
        }

        requestTimestamps.put(clientKey, currentTime);
        return false;
    }

    /**
     * 获取客户端列表（分页）- 增加频率限制
     */
    @GetMapping("/clients")
    public Map<String, Object> getClients(@RequestParam(defaultValue = "1") int _page,
                                         @RequestParam(defaultValue = "20") int _limit,
                                         javax.servlet.http.HttpServletRequest request) {

        // 获取客户端标识（IP地址）
        String clientIp = getClientIpAddress(request);
        String clientKey = "clients_" + clientIp;

        // 检查请求频率
        if (isRequestTooFrequent(clientKey)) {
            logger.warn("客户端 {} 请求过于频繁，拒绝服务", clientIp);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", 429);
            errorResult.put("message", "请求过于频繁，请稍后再试");
            return errorResult;
        }

        logger.info("获取客户端列表请求 - page: {}, limit: {}, IP: {}", _page, _limit, clientIp);

        // 先同步订阅信息
        mqttStatsService.syncSubscriptionsFromMicaApi();

        // 使用MqttClientService获取客户端列表
        return mqttClientService.getClientsList(_page, _limit);
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(javax.servlet.http.HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }

    /**
     * 获取客户端详情 - 返回真实的客户端信息
     */
    @GetMapping("/clients/info")
    public Map<String, Object> getClientInfo(@RequestParam String clientId,
                                            javax.servlet.http.HttpServletRequest request) {
        // 获取客户端标识（IP地址）
        String clientIp = getClientIpAddress(request);
        String clientKey = "client_info_" + clientIp;

        // 检查请求频率
        if (isRequestTooFrequent(clientKey)) {
            logger.warn("客户端 {} 请求过于频繁，拒绝服务", clientIp);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", 429);
            errorResult.put("message", "请求过于频繁，请稍后再试");
            return errorResult;
        }

        // 使用MqttClientService获取客户端详情
        return mqttClientService.getClientInfo(clientId);
    }

    /**
     * 获取客户端订阅信息 - 返回真实的订阅数据
     */
    @GetMapping("/client/subscriptions")
    public Map<String, Object> getClientSubscriptions(@RequestParam String clientId) {
        // 先从mica-mqtt API同步最新的订阅信息
        mqttStatsService.syncSubscriptionsFromMicaApi();

        // 使用MqttClientService获取客户端订阅信息
        return mqttClientService.getClientSubscriptions(clientId);
    }

    /**
     * 获取统计信息 - 返回真实的MQTT服务器统计数据
     */
    @GetMapping("/stats")
    public Map<String, Object> getStats() {
        // 使用MqttClientService获取统计信息
        return mqttClientService.getStats();
    }

    /**
     * 发布MQTT消息
     */
    @PostMapping("/mqtt/publish")
    public Map<String, Object> publishMessage(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String topic = (String) request.get("topic");
            String payload = (String) request.get("payload");
            Integer qos = (Integer) request.getOrDefault("qos", 0);
            Boolean retain = (Boolean) request.getOrDefault("retain", false);
            String clientId = (String) request.getOrDefault("clientId", "httpApi");
            
            // 使用MqttServerTemplate发布消息
            boolean success = mqttServerTemplate.publishAll(topic, payload.getBytes(StandardCharsets.UTF_8));
            
            if (success) {
                result.put("code", 1);
                result.put("message", "Message published successfully");
            } else {
                result.put("code", 105);
                result.put("message", "Failed to publish message");
            }
        } catch (Exception e) {
            result.put("code", 105);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 批量发布MQTT消息
     */
    @PostMapping("/mqtt/publish/batch")
    public Map<String, Object> publishBatchMessages(@RequestBody List<Map<String, Object>> messages) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            int successCount = 0;
            for (Map<String, Object> message : messages) {
                String topic = (String) message.get("topic");
                String payload = (String) message.get("payload");
                
                boolean success = mqttServerTemplate.publishAll(topic, payload.getBytes(StandardCharsets.UTF_8));
                if (success) {
                    successCount++;
                }
            }
            
            result.put("code", 1);
            result.put("message", "Batch publish completed. Success: " + successCount + "/" + messages.size());
        } catch (Exception e) {
            result.put("code", 105);
            result.put("message", "Batch publish error: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 订阅主题 - 处理真实的订阅请求
     */
    @PostMapping("/mqtt/subscribe")
    public Map<String, Object> subscribe(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String topic = (String) request.get("topic");
            String clientId = (String) request.get("clientId");
            Integer qos = (Integer) request.getOrDefault("qos", 0);

            if (topic == null || topic.trim().isEmpty()) {
                result.put("code", 104);
                result.put("message", "Topic is required");
                return result;
            }

            if (clientId == null || clientId.trim().isEmpty()) {
                result.put("code", 104);
                result.put("message", "ClientId is required");
                return result;
            }

            // 更新统计数据中的订阅信息
            mqttStatsService.onClientSubscribe(clientId, topic);

            result.put("code", 1);
            result.put("message", "Subscribe request processed successfully");
            Map<String, Object> data = new HashMap<>();
            data.put("topic", topic);
            data.put("clientId", clientId);
            data.put("qos", qos);
            result.put("data", data);

            logger.info("订阅请求处理成功 - ClientId: {}, Topic: {}, QoS: {}", clientId, topic, qos);

        } catch (Exception e) {
            logger.error("处理订阅请求失败", e);
            result.put("code", 105);
            result.put("message", "Subscribe request failed: " + e.getMessage());
        }

        return result;
    }

    /**
     * 批量订阅主题
     */
    @PostMapping("/mqtt/subscribe/batch")
    public Map<String, Object> subscribeBatch(@RequestBody List<Map<String, Object>> subscriptions) {
        Map<String, Object> result = new HashMap<>();
        
        result.put("code", 1);
        result.put("message", "Batch subscribe request processed");
        
        return result;
    }

    /**
     * 取消订阅主题 - 处理真实的取消订阅请求
     */
    @PostMapping("/mqtt/unsubscribe")
    public Map<String, Object> unsubscribe(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String topic = (String) request.get("topic");
            String clientId = (String) request.get("clientId");

            if (topic == null || topic.trim().isEmpty()) {
                result.put("code", 104);
                result.put("message", "Topic is required");
                return result;
            }

            if (clientId == null || clientId.trim().isEmpty()) {
                result.put("code", 104);
                result.put("message", "ClientId is required");
                return result;
            }

            // 更新统计数据中的订阅信息
            mqttStatsService.onClientUnsubscribe(clientId, topic);

            result.put("code", 1);
            result.put("message", "Unsubscribe request processed successfully");
            Map<String, Object> data = new HashMap<>();
            data.put("topic", topic);
            data.put("clientId", clientId);
            result.put("data", data);

            logger.info("取消订阅请求处理成功 - ClientId: {}, Topic: {}", clientId, topic);

        } catch (Exception e) {
            logger.error("处理取消订阅请求失败", e);
            result.put("code", 105);
            result.put("message", "Unsubscribe request failed: " + e.getMessage());
        }

        return result;
    }

    /**
     * 批量取消订阅主题
     */
    @PostMapping("/mqtt/unsubscribe/batch")
    public Map<String, Object> unsubscribeBatch(@RequestBody List<Map<String, Object>> unsubscriptions) {
        Map<String, Object> result = new HashMap<>();
        
        result.put("code", 1);
        result.put("message", "Batch unsubscribe request processed");
        
        return result;
    }

    /**
     * 断开客户端连接 - 真正断开指定客户端
     */
    @PostMapping("/clients/delete")
    public Map<String, Object> deleteClient(@RequestParam String clientId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查客户端是否存在
            Map<String, MqttStatsService.ClientInfo> connectedClients = mqttStatsService.getConnectedClients();
            if (!connectedClients.containsKey(clientId)) {
                result.put("code", 104);
                result.put("message", "Client not found: " + clientId);
                return result;
            }

            // 使用MqttServerTemplate断开客户端连接
            mqttServerTemplate.close(clientId);

            result.put("code", 1);
            result.put("message", "Client disconnected successfully: " + clientId);
            logger.info("客户端 {} 已被管理员断开连接", clientId);
        } catch (Exception e) {
            logger.error("断开客户端连接失败: {}", clientId, e);
            result.put("code", 105);
            result.put("message", "Error disconnecting client: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取所有客户端及其订阅主题 - 聚合查询接口
     */
    @GetMapping("/clients/subscriptions")
    public Map<String, Object> getAllClientsSubscriptions(javax.servlet.http.HttpServletRequest request) {
        // 获取客户端标识（IP地址）
        String clientIp = getClientIpAddress(request);
        String clientKey = "clients_subscriptions_" + clientIp;

        // 检查请求频率
        if (isRequestTooFrequent(clientKey)) {
            logger.warn("客户端 {} 请求过于频繁，拒绝服务", clientIp);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", 429);
            errorResult.put("message", "请求过于频繁，请稍后再试");
            return errorResult;
        }

        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> clientsWithSubscriptions = new ArrayList<>();

        try {
            logger.info("获取所有客户端订阅信息请求 - IP: {}", clientIp);

            // 先同步订阅信息
            mqttStatsService.syncSubscriptionsFromMicaApi();

            // 获取所有客户端
            Map<String, MqttStatsService.ClientInfo> connectedClients = mqttStatsService.getConnectedClients();

            for (MqttStatsService.ClientInfo clientInfo : connectedClients.values()) {
                try {
                    Map<String, Object> clientData = new HashMap<>();
                    clientData.put("clientId", clientInfo.clientId);
                    clientData.put("username", clientInfo.username);
                    clientData.put("connectedAt", clientInfo.connectedAt);
                    clientData.put("ipAddress", clientInfo.ipAddress);

                    // 获取该客户端的订阅信息 - 使用getClientSubscriptions方法
                    Set<String> clientSubscriptions = mqttStatsService.getClientSubscriptions(clientInfo.clientId);
                    List<String> subscriptions = new ArrayList<>(clientSubscriptions);
                    clientData.put("subscriptions", subscriptions);
                    clientData.put("subscriptionCount", subscriptions.size());

                    clientsWithSubscriptions.add(clientData);
                } catch (Exception e) {
                    logger.error("获取客户端 {} 订阅信息失败", clientInfo.clientId, e);
                    // 即使单个客户端失败，也继续处理其他客户端
                    Map<String, Object> clientData = new HashMap<>();
                    clientData.put("clientId", clientInfo.clientId);
                    clientData.put("username", clientInfo.username);
                    clientData.put("connectedAt", clientInfo.connectedAt);
                    clientData.put("ipAddress", clientInfo.ipAddress);
                    clientData.put("subscriptions", new ArrayList<>());
                    clientData.put("subscriptionCount", 0);
                    clientData.put("error", "获取订阅信息失败");
                    clientsWithSubscriptions.add(clientData);
                }
            }

            result.put("code", 1);
            result.put("data", clientsWithSubscriptions);
            result.put("totalClients", clientsWithSubscriptions.size());
            
            logger.info("成功获取所有客户端订阅信息，共 {} 个客户端", clientsWithSubscriptions.size());
        } catch (Exception e) {
            logger.error("获取所有客户端订阅信息失败", e);
            result.put("code", 105);
            result.put("message", "获取所有客户端订阅信息失败: " + e.getMessage());
            result.put("data", new ArrayList<>());
        }

        return result;
    }

    /**
     * 创建端点信息
     */
    private Map<String, String> createEndpoint(String method, String path) {
        Map<String, String> endpoint = new HashMap<>();
        endpoint.put("method", method);
        endpoint.put("path", path);
        return endpoint;
    }
}

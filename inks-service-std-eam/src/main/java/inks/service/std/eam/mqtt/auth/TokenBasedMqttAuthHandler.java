package inks.service.std.eam.mqtt.auth;

import inks.common.redis.service.RedisService;
import inks.service.std.eam.mqtt.service.MqttConnectionManager;
import org.dromara.mica.mqtt.core.server.auth.IMqttServerAuthHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.tio.core.ChannelContext;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 基于 Token 的 MQTT 认证处理器
 * 用户名可直接作为 Token，向后兼容用户名密码方式
 * 使用Redis存储token和设备ID的映射关系
 */
@Component
public class TokenBasedMqttAuthHandler implements IMqttServerAuthHandler {

    private static final Logger logger = LoggerFactory.getLogger(TokenBasedMqttAuthHandler.class);

    /** Redis Key前缀 */
    private static final String REDIS_TOKEN_PREFIX = "mqtt:token:";
    private static final String REDIS_DEVICE_PREFIX = "mqtt:device:";

    @Resource
    private RedisService redisService;

    @Resource
    private MqttConnectionManager mqttConnectionManager;



    public TokenBasedMqttAuthHandler() {
        logger.info("=== TokenBasedMqttAuthHandler 已创建并注册（Redis版本） ===");
    }

    @Override
    public boolean authenticate(ChannelContext context,
                                String uniqueId,
                                String clientId,
                                String userName,
                                String password) {

        String ip = context.getClientNode() != null ? context.getClientNode().getIp() : "unknown";

        logger.info("MQTT 认证请求 - uniqueId:{}, clientId:{}, userName:{}, ip:{}",
                uniqueId, clientId, userName, ip);

        // 1. 用户名不能为空
        if (userName == null || userName.trim().isEmpty()) {
            logger.warn("认证失败：用户名为空 - clientId:{}", clientId);
            return false;
        }

        // 2. Redis Token 认证
        try {
            String deviceId = redisService.getCacheObject(REDIS_TOKEN_PREFIX + userName);
            if (deviceId != null && !deviceId.trim().isEmpty()) {
                logger.info("Redis Token认证成功 - clientId:{}, token:{}, deviceId:{} 认证通过后把用户名（token）和设备ID 存入ChannelContext",
                           clientId, userName, deviceId);
                // 认证通过后把用户名（token）和设备ID塞进 ChannelContext
                context.set("username", userName);
                context.set("deviceId", deviceId);
                context.set("clientId", clientId);

                // 注册MQTT连接到管理器
                mqttConnectionManager.registerConnection(userName, deviceId, context);

                return true;
            }
        } catch (Exception e) {
            logger.warn("Redis Token认证异常，尝试降级处理 - clientId:{}, error:{}",
                       clientId, e.getMessage());
        }



        // 3. 兼容用户名密码
        if (Objects.equals("inks", userName) && Objects.equals("8866", password)) {
            logger.info("用户名密码认证成功 - clientId:{}, userName:{}", clientId, userName);
            context.set("username", userName);
            context.set("clientId", clientId);

            return true;
        }

        logger.warn("认证失败 - clientId:{}, userName:{}", clientId, userName);
        return false;
    }

    /* ===== 辅助方法：动态管理 Token（Redis版本） ===== */

    /**
     * 添加Token到Redis
     * @param token 设备token
     * @param deviceId 设备ID
     */
    public void addTokenToRedis(String token, String deviceId) {
        if (token == null || token.trim().isEmpty() || deviceId == null || deviceId.trim().isEmpty()) {
            logger.warn("添加Token失败：token或deviceId为空");
            return;
        }

        try {
            redisService.setCacheObject(REDIS_TOKEN_PREFIX + token, deviceId);
            redisService.setCacheObject(REDIS_DEVICE_PREFIX + deviceId, token);
            logger.info("添加Token到Redis成功 - token:{}, deviceId:{}", token, deviceId);
        } catch (Exception e) {
            logger.error("添加Token到Redis失败 - token:{}, deviceId:{}, error:{}",
                        token, deviceId, e.getMessage());
        }
    }

    /**
     * 从Redis移除Token
     * @param token 设备token
     */
    public void removeTokenFromRedis(String token) {
        if (token == null || token.trim().isEmpty()) {
            logger.warn("移除Token失败：token为空");
            return;
        }

        try {
            String deviceId = redisService.getCacheObject(REDIS_TOKEN_PREFIX + token);
            redisService.deleteObject(REDIS_TOKEN_PREFIX + token);
            if (deviceId != null) {
                redisService.deleteObject(REDIS_DEVICE_PREFIX + deviceId);
            }
            logger.info("从Redis移除Token成功 - token:{}, deviceId:{}", token, deviceId);
        } catch (Exception e) {
            logger.error("从Redis移除Token失败 - token:{}, error:{}", token, e.getMessage());
        }
    }

    /**
     * 通过token获取设备ID
     * @param token 设备token
     * @return 设备ID，如果不存在返回null
     */
    public String getDeviceIdByToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }

        try {
            return redisService.getCacheObject(REDIS_TOKEN_PREFIX + token);
        } catch (Exception e) {
            logger.error("从Redis获取设备ID失败 - token:{}, error:{}", token, e.getMessage());
            return null;
        }
    }


}


package inks.service.std.eam.mqtt.controller;


import org.dromara.mica.mqtt.spring.server.MqttServerTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * MQTT HTTP API控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mqtt")
public class MqttController {

    @Autowired
    private MqttServerTemplate mqttServerTemplate;

    /**
     * 发布消息到指定主题
     */
    @PostMapping("/publish")
    public Map<String, Object> publishMessage(@RequestParam String topic,
                                            @RequestParam String message,
                                            @RequestParam(defaultValue = "0") int qos) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 使用正确的方法名和参数
            boolean success = mqttServerTemplate.publishAll(topic, message.getBytes());
            result.put("success", success);
            result.put("message", "Message published successfully");
            result.put("topic", topic);
            result.put("payload", message);
            result.put("qos", qos);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "Failed to publish message: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取服务器状态
     */
    @GetMapping("/status")
    public Map<String, Object> getServerStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "running");
        status.put("server", "MQTT Demo Server");
        status.put("timestamp", System.currentTimeMillis());
        return status;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "mqtt-demo");
        return health;
    }
}

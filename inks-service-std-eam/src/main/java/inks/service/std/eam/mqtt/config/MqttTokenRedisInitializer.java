package inks.service.std.eam.mqtt.config;

import inks.common.redis.service.RedisService;
import inks.service.std.eam.domain.vo.DeviceTokenVO;
import inks.service.std.eam.mqtt.auth.TokenBasedMqttAuthHandler;
import inks.service.std.eam.service.IotDeviceService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * MQTT Token Redis数据初始化器
 * 在应用启动时从数据库同步token数据到Redis
 * 提供数据一致性检查和修复功能
 * 
 * <AUTHOR>
 */
@Component
public class MqttTokenRedisInitializer implements ApplicationRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(MqttTokenRedisInitializer.class);
    
    /** Redis Key前缀 */
    private static final String REDIS_TOKEN_PREFIX = "mqtt:token:";
    private static final String REDIS_DEVICE_PREFIX = "mqtt:device:";

    /** Redis健康检查Key - 用于验证Redis连接是否正常，避免在Redis不可用时进行数据同步 */
    private static final String REDIS_HEALTH_CHECK_KEY = "mqtt:health:check";
    
    /** Token过期时间（7天） */
    private static final long TOKEN_EXPIRE_DAYS = 7;
    
    @Resource
    private RedisService redisService;
    
    @Resource
    private IotDeviceService iotDeviceService;
    
    @Resource
    private TokenBasedMqttAuthHandler tokenBasedMqttAuthHandler;
    
    @Override
    public void run(ApplicationArguments args) {
        logger.info("开始MQTT Token Redis数据初始化...");
        
        try {
            // 1. Redis健康检查
            if (!performRedisHealthCheck()) {
                logger.error("Redis健康检查失败，跳过token数据初始化");
                return;
            }
            
            // 2. 从数据库同步token数据到Redis
            int syncCount = syncTokensFromDatabaseToRedis();
            
            // 3. 数据一致性检查
            performDataConsistencyCheck();
            
            logger.info("MQTT Token Redis数据初始化完成，同步{}个token", syncCount);
            
        } catch (Exception e) {
            logger.error("MQTT Token Redis数据初始化失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Redis健康检查
     * @return 是否健康
     */
    public boolean performRedisHealthCheck() {
        try {
            String testValue = "health_check_" + System.currentTimeMillis();
            redisService.setCacheObject(REDIS_HEALTH_CHECK_KEY, testValue, 60L, TimeUnit.SECONDS);
            String retrievedValue = redisService.getCacheObject(REDIS_HEALTH_CHECK_KEY);
            
            boolean isHealthy = testValue.equals(retrievedValue);
            if (isHealthy) {
                logger.info("Redis健康检查通过");
                redisService.deleteObject(REDIS_HEALTH_CHECK_KEY);
            } else {
                logger.error("Redis健康检查失败：写入和读取的值不一致");
            }
            return isHealthy;
        } catch (Exception e) {
            logger.error("Redis健康检查异常: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 从数据库同步token数据到Redis
     * @return 同步的token数量
     */
    public int syncTokensFromDatabaseToRedis() {
        try {
            logger.info("开始从数据库同步token数据到Redis...");
            
            List<DeviceTokenVO> devices = iotDeviceService.getAllDevicesWithToken();
            int successCount = 0;
            int failCount = 0;
            
            for (DeviceTokenVO device : devices) {
                String token = device.getToken();
                if (StringUtils.isNotBlank(token)) {
                    try {
                        // 使用TokenBasedMqttAuthHandler的方法确保一致性
                        tokenBasedMqttAuthHandler.addTokenToRedis(token, device.getId());
                        // 设置过期时间（可选）
                        redisService.expire(REDIS_TOKEN_PREFIX + token, TOKEN_EXPIRE_DAYS, TimeUnit.DAYS);
                        redisService.expire(REDIS_DEVICE_PREFIX + device.getId(), TOKEN_EXPIRE_DAYS, TimeUnit.DAYS);
                        successCount++;
                    } catch (Exception e) {
                        logger.warn("同步token失败 - deviceId:{}, token:{}, error:{}", 
                                   device.getId(), token, e.getMessage());
                        failCount++;
                    }
                }
            }
            
            logger.info("数据库token同步完成 - 成功:{}, 失败:{}, 总计:{}", 
                       successCount, failCount, devices.size());
            return successCount;
            
        } catch (Exception e) {
            logger.error("从数据库同步token数据失败: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 数据一致性检查
     * @return 检查结果
     */
    public Map<String, Object> performDataConsistencyCheck() {
        logger.info("开始数据一致性检查...");
        
        try {
            List<DeviceTokenVO> dbDevices = iotDeviceService.getAllDevicesWithToken();
            int dbTokenCount = dbDevices.size();
            int redisTokenCount = 0;
            int consistentCount = 0;
            int inconsistentCount = 0;
            
            for (DeviceTokenVO device : dbDevices) {
                if (device.getToken() != null && !device.getToken().trim().isEmpty()) {
                    String redisDeviceId = redisService.getCacheObject(REDIS_TOKEN_PREFIX + device.getToken());
                    if (redisDeviceId != null) {
                        redisTokenCount++;
                        if (device.getId().equals(redisDeviceId)) {
                            consistentCount++;
                        } else {
                            inconsistentCount++;
                            logger.warn("数据不一致 - token:{}, DB deviceId:{}, Redis deviceId:{}", 
                                       device.getToken(), device.getId(), redisDeviceId);
                        }
                    }
                }
            }
            
            logger.info("数据一致性检查完成 - DB token数:{}, Redis token数:{}, 一致:{}, 不一致:{}", 
                       dbTokenCount, redisTokenCount, consistentCount, inconsistentCount);
            
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("dbTokenCount", dbTokenCount);
            result.put("redisTokenCount", redisTokenCount);
            result.put("consistentCount", consistentCount);
            result.put("inconsistentCount", inconsistentCount);
            result.put("isConsistent", inconsistentCount == 0);
            
            return result;
            
        } catch (Exception e) {
            logger.error("数据一致性检查失败: {}", e.getMessage(), e);
            return new java.util.HashMap<>();
        }
    }
    
    /**
     * 修复数据不一致问题
     * @return 修复的token数量
     */
    public int repairDataInconsistency() {
        logger.info("开始修复数据不一致问题...");
        
        try {
            // 清除所有Redis中的token数据
            // 注意：这里简化处理，实际生产环境可能需要更精细的修复策略
            
            // 重新从数据库同步
            return syncTokensFromDatabaseToRedis();
            
        } catch (Exception e) {
            logger.error("修复数据不一致失败: {}", e.getMessage(), e);
            return 0;
        }
    }
}

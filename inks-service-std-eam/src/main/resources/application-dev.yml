server:
  tomcat:
    uri-encoding: UTF-8
#spring数据源
spring:
  cloud:
    nacos:
      discovery:
        # Nacos 注册中心 8848
        server-addr: **************:8848
        username: nacos
        password: inks0820
        ip: *************
  datasource:
    #MYsql连接字符串
    url: ***************************************************************************************************************************************************
    username: root
    password: asd@123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 30
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: **************
    # Redis服务器连接端口
    port: 56379
    # Redis服务器连接密码（默认为空）
    password: asd@123456
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000
  mail:
    username: <EMAIL>
    password: ASDqwe@!@#
    host: smtp.qiye.aliyun.com
    default-encoding: UTF-8
    properties:
      mail.smtp.socketFactory.fallback: true
      mail.smtp.starttls.enable: true
    #    toEmail: <EMAIL>  #登录日志的收件人
    toEmail: <EMAIL>  #登录日志的收件人
  web: #配置静态资源访问路径
    resources:
      static-locations: classpath:/static/, classpath:/h5/
  mvc:
    view:
      suffix: .html
mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.service.**.domain
  #配置打印SQL语句到控制台

## MQTT##
mqtt:
  server:
    enabled: true               # 是否开启服务端，默认：true
    #    ip: 0.0.0.0                # 服务端 ip 默认为空，0.0.0.0，建议不要设置
    port: 1883                  # 端口，默认：1883
    name: Mica-Mqtt-Server      # 名称，默认：Mica-Mqtt-Server
    heartbeat-timeout: 120000   # 心跳超时，单位毫秒，默认: 1000 * 120
    read-buffer-size: 8KB       # 接收数据的 buffer size，默认：8k
    max-bytes-in-message: 10MB  # 消息解析最大 bytes 长度，默认：10M
    auth:
      enable: true             # 开启认证，使用自定义认证处理器
      username: inks            # mqtt 认证用户名（保留配置但不使用）
      password: 8866            # mqtt 认证密码（保留配置但不使用）
    debug: true                 # 如果开启 prometheus 指标收集建议关闭
    stat-enable: true           # 开启指标收集，debug 和 prometheus 开启时需要打开，默认开启，关闭节省内存
    web-port: 8083             # http、websocket 端口，默认：8083
    websocket-enable: true      # 是否开启 websocket，默认： true
    http-enable: true           # 是否开启 http api，默认： false
    http-basic-auth:
      enable: false              # 是否开启 http basic auth，默认： false
      username: "mica"          # http basic auth 用户名
      password: "mica"          # http basic auth 密码
    ssl: # mqtt tcp ssl 认证
      enabled: false            # 是否开启 ssl 认证，2.1.0 开始支持双向认证
      keystore-path:            # 必须参数：ssl keystore 目录，支持 classpath:/ 路径。
      keystore-pass:            # 必选参数：ssl keystore 密码
      truststore-path:          # 可选参数：ssl 双向认证 truststore 目录，支持 classpath:/ 路径。
      truststore-pass:          # 可选参数：ssl 双向认证 truststore 密码
      client-auth: none         # 是否需要客户端认证（双向认证），默认：NONE（不需要）
#mqtt:
#  server:
#    enabled: true                 # 是否开启服务端，默认：true
#    name: Mica-Mqtt-Server        # 名称，默认：Mica-Mqtt-Server
#    heartbeat-timeout: 120000     # 心跳超时，单位毫秒，默认: 1000 * 120
#    read-buffer-size: 8KB         # 接收数据的 buffer size，默认：8k
#    max-bytes-in-message: 10MB    # 消息解析最大 bytes 长度，默认：10M
#    auth:
#      enable: true                # 是否开启 mqtt 认证
#      username: mica              # mqtt 认证用户名
#      password: mica              # mqtt 认证密码
#    debug: true                   # 如果开启 prometheus 指标收集建议关闭
#    stat-enable: true             # 开启指标收集，debug 和 prometheus 开启时需要打开，默认开启，关闭节省内存
#    mqtt-listener:                # mqtt 监听器
#      enable: true                # 是否开启，默认：false
#      port: 1883                  # 端口，默认：1883
#    mqtt-ssl-listener:            # mqtt ssl 监听器
#      enable: false               # 是否开启，默认：false
#      port: 8883                  # 端口，默认：8883
#    ws-listener:                  # websocket mqtt 监听器
#      enable: true                # 是否开启，默认：false
#      port: 8083                  # websocket 端口，默认：8083
#    wss-listener:                 # websocket ssl mqtt 监听器
#      enable: false               # 是否开启，默认：false
#      port: 8084                  # 端口，默认：8084
#    http-listener:                # mica-mqtt自带的HTTP API (端口18083)
#      enable: true
#      port: 18083
#      basic-auth:                 # 基础认证
#        enable: true
#        username: mica
#        password: mica


